import { NextRequest } from "next/server"
import { redirect } from "next/navigation"

export async function GET(request: NextRequest, { params }: { params: { provider: string } }) {
  const { provider } = params
  const searchParams = request.nextUrl.searchParams

  // Get query parameters
  const invitedEmail = searchParams.get("invited_email")
  const referralCode = searchParams.get("referral_code")
  const callbackUrl = searchParams.get("callback")
  const onboardingComplete = searchParams.get("onboarding_complete")

  // For now, redirect to a temporary OAuth completion page that will handle the provider sign-in
  const urlParams = new URLSearchParams()
  if (invitedEmail) urlParams.set("invited_email", invitedEmail)
  if (referralCode) urlParams.set("referral_code", referralCode)
  if (callbackUrl) urlParams.set("callback", callbackUrl)
  if (onboardingComplete) urlParams.set("onboarding_complete", onboardingComplete)
  urlParams.set("provider", provider)

  const redirectUrl = `/auth/oauth/complete${urlParams.toString() ? `?${urlParams.toString()}` : ""}`

  return redirect(redirectUrl)
}
