"use server"

import { doc, setDoc, serverTimestamp, getDoc } from "firebase/firestore"
import { updateProfile } from "firebase/auth"
import { auth, db } from "@/lib/firebase"
import { uploadProfilePictureAction } from "../../signup/actions/upload-profile-picture"
import { ReferralAdminService } from "@/lib/domains/referral/referral.admin.service"

export interface CompleteUserProfileData {
  userId: string
  email: string
  name: string
  bio?: string
  location?: string
  locationPlaceId?: string
  selectedTravelPreferences: string[]
  budget: string
  selectedAvailability: string[]
  selectedMonths: string[]
  selectedTravelGroups: string[]
  referralCode?: string
  invitedEmail?: string // Email that was invited (for validation)
  photoURL?: string // From OAuth provider
  authProvider: string // Which OAuth provider was used
}

export interface CompleteUserProfileResult {
  success: boolean
  error?: string
  redirectUrl?: string
}

export async function completeUserProfileAction(
  userData: CompleteUserProfileData,
  profilePictureFormData?: FormData
): Promise<CompleteUserProfileResult> {
  console.log("Complete User Profile Action:", {
    userId: userData.userId,
    email: userData.email,
    authProvider: userData.authProvider,
    hasProfilePicture: !!profilePictureFormData?.get("file"),
  })

  try {
    // Validate required fields
    if (!userData.userId || !userData.email || !userData.name) {
      return {
        success: false,
        error: "Missing required fields: userId, email, and name are required",
      }
    }

    // Validate email match if this is an invitation signup
    if (userData.invitedEmail) {
      const oauthEmail = userData.email.toLowerCase()
      const expectedEmail = userData.invitedEmail.toLowerCase()

      if (oauthEmail !== expectedEmail) {
        return {
          success: false,
          error: `Email mismatch: You were invited as ${userData.invitedEmail}, but signed in with ${userData.email}. Please sign in with the invited email address.`,
        }
      }
    }

    // Check if user document already exists (to prevent duplicate profile completion)
    const userRef = doc(db, "users", userData.userId)
    const existingUser = await getDoc(userRef)

    if (existingUser.exists() && !existingUser.data()?.newUser) {
      console.log("User profile already completed, redirecting to dashboard")
      return {
        success: true,
        redirectUrl: "/dashboard",
      }
    }

    let finalProfilePictureURL: string | null = userData.photoURL || null

    // Upload new profile picture if provided (overrides OAuth photo)
    if (profilePictureFormData?.get("file")) {
      console.log("Uploading new profile picture...")
      const uploadResult = await uploadProfilePictureAction(profilePictureFormData)

      if (!uploadResult.success) {
        return {
          success: false,
          error: `Profile picture upload failed: ${uploadResult.error}`,
        }
      }

      finalProfilePictureURL = uploadResult.url || null
      console.log("New profile picture uploaded successfully:", finalProfilePictureURL)
    }

    // Update Firebase Auth profile if we have a new photo or name
    const currentUser = auth.currentUser
    if (currentUser) {
      await updateProfile(currentUser, {
        displayName: userData.name,
        photoURL: finalProfilePictureURL,
      })
      console.log("Firebase Auth profile updated")
    }

    // 1. Create/Update user document in Firestore
    await setDoc(
      userRef,
      {
        uid: userData.userId,
        email: userData.email,
        displayName: userData.name,
        photoURL: finalProfilePictureURL,
        bio: userData.bio || "",
        location: userData.location || null,
        locationPlaceId: userData.locationPlaceId || null,
        createdAt: existingUser.exists() ? existingUser.data()?.createdAt : serverTimestamp(),
        updatedAt: serverTimestamp(),
        travelPreferences: userData.selectedTravelPreferences,
        budgetRange: userData.budget,
        availabilityPreferences: userData.selectedAvailability,
        preferredTravelSeasons: userData.selectedMonths,
        travelGroupPreferences: userData.selectedTravelGroups,
        newUser: false, // Mark as completed
        firstLogin: existingUser.exists() ? existingUser.data()?.firstLogin : serverTimestamp(),
        authProvider: userData.authProvider,
        profileCompleted: true,
        profileCompletedAt: serverTimestamp(),
      },
      { merge: true }
    )

    // 2. Initialize/Update user preferences document
    const preferencesRef = doc(db, "userPreferences", userData.userId)
    await setDoc(
      preferencesRef,
      {
        userId: userData.userId,
        theme: "system",
        location: userData.location || null,
        locationPlaceId: userData.locationPlaceId || null,
        travelPreferences: userData.selectedTravelPreferences,
        budgetRange:
          userData.budget === "budget-friendly"
            ? [0, 500]
            : userData.budget === "mid-range"
              ? [500, 2000]
              : [2000, 10000],
        availabilityPreferences: userData.selectedAvailability,
        preferredTravelSeasons: userData.selectedMonths,
        travelGroupPreferences: userData.selectedTravelGroups,
        aiEnabled: true,
        proactiveSuggestions: true,
        notificationsEnabled: true,
        emailNotifications: true,
        pushNotifications: true,
        tripUpdatesNotifications: true,
        squadMessagesNotifications: true,
        invitationNotifications: true,
        aiSuggestionsNotifications: true,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      },
      { merge: true }
    )

    // 3. Initialize user AI usage document if it doesn't exist
    const aiUsageRef = doc(db, "userAiUsage", userData.userId)
    const existingAiUsage = await getDoc(aiUsageRef)

    if (!existingAiUsage.exists()) {
      await setDoc(aiUsageRef, {
        userId: userData.userId,
        aiUsageToday: 0,
        aiUsageThisWeek: 0,
        aiUsageLastReset: serverTimestamp(),
        aiUsageWeekStart: serverTimestamp(),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })
    }

    // 4. Create free subscription entry for new user if it doesn't exist
    try {
      const { FlatSubscriptionService } = await import(
        "@/lib/domains/user-subscription/flat-subscription.service"
      )
      const freeSubscriptionResult = await FlatSubscriptionService.addFreeSubscription(
        userData.userId
      )
      if (freeSubscriptionResult.success) {
        console.log("Free subscription ensured for user:", userData.userId)
      } else {
        console.error("Failed to ensure free subscription for user:", freeSubscriptionResult.error)
        // Don't fail profile completion if subscription creation fails
      }
    } catch (error) {
      console.error("Error ensuring free subscription for user:", error)
      // Don't fail profile completion if subscription creation fails
    }

    // 5. Generate referral code for the user if they don't have one
    try {
      const newUserReferralCode = await ReferralAdminService.generateReferralCode()
      await ReferralAdminService.createReferralCode(newUserReferralCode, userData.userId)
      console.log("Referral code ensured for user:", newUserReferralCode)
    } catch (error) {
      console.error("Error ensuring referral code for user:", error)
      // Don't fail the profile completion if referral code creation fails
    }

    // 6. Process referral code if provided
    if (userData.referralCode) {
      console.log(
        "🎯 Processing referral code:",
        userData.referralCode,
        "for user:",
        userData.userId
      )
      try {
        const referralResult = await ReferralAdminService.processReferral(
          userData.referralCode,
          userData.userId,
          userData.email
        )

        console.log("🔄 Referral processing result:", referralResult)

        if (referralResult.success) {
          console.log("✅ Referral processed successfully:", referralResult)

          // Check for newly unlocked perks for the referrer
          if (referralResult.perksUnlocked && referralResult.perksUnlocked.length > 0) {
            console.log("🎁 Perks unlocked for referrer:", referralResult.perksUnlocked)
          }
        } else {
          console.error("❌ Failed to process referral:", referralResult.error)
        }
      } catch (error) {
        console.error("💥 Error processing referral code:", error)
        // Don't fail the profile completion if referral processing fails
      }
    } else {
      console.log("ℹ️ No referral code provided during profile completion")
    }

    console.log("User profile completed successfully")

    return {
      success: true,
      redirectUrl: "/dashboard",
    }
  } catch (error: any) {
    console.error("Complete user profile error:", error)

    return {
      success: false,
      error: error.message || "Something went wrong. Please try again.",
    }
  }
}
