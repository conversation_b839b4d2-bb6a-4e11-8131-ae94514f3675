"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { useRouter, useSearchPara<PERSON> } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { ArrowLeft, ArrowRight, X } from "lucide-react"
import { MonthSelector } from "@/components/month-selector"
import { LocationInputWithGeolocation } from "@/components/location-input-with-geolocation"
import { ImageUploadWithCompression } from "@/components/ui/image-upload-with-compression"
import { toast } from "@/components/ui/use-toast"
import { Logo } from "@/components/ui/logo"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { completeUserProfileAction } from "../actions/complete-user-profile"
import {
  ALLOWED_TRAVEL_TYPES,
  ALLOWED_AVAILABILITY_PREFERENCES,
  ALLOWED_TRAVEL_GROUP_PREFERENCES,
} from "@/lib/constants/travel-types"

export function ProfileCompletionForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const user = useUser()
  
  const [step, setStep] = useState(1)
  const [selectedMonths, setSelectedMonths] = useState<string[]>([])
  const [selectedTravelPreferences, setSelectedTravelPreferences] = useState<string[]>([])
  const [selectedAvailability, setSelectedAvailability] = useState<string[]>([])
  const [selectedTravelGroups, setSelectedTravelGroups] = useState<string[]>([])
  const [budget, setBudget] = useState("mid-range")
  const [location, setLocation] = useState("")
  const [locationPlaceId, setLocationPlaceId] = useState<string | undefined>()
  const [loading, setLoading] = useState(false)
  const [callbackUrl, setCallbackUrl] = useState<string | null>(null)
  const [invitedEmail, setInvitedEmail] = useState<string | null>(null)
  const [referralCode, setReferralCode] = useState<string>("")
  const [profilePicture, setProfilePicture] = useState<File | null>(null)
  const totalSteps = 3 // Reduced from 4 since we don't need email/password step

  // Extract parameters from search params
  useEffect(() => {
    const callback = searchParams.get("callback")
    if (callback) {
      setCallbackUrl(callback)
    }

    const email = searchParams.get("invited_email")
    if (email) {
      setInvitedEmail(email)
    }

    const referral = searchParams.get("referral_code")
    if (referral) {
      setReferralCode(referral)
    }
  }, [searchParams])

  // Form data (no email/password needed since user is already authenticated)
  const [formData, setFormData] = useState({
    name: "",
    bio: "",
  })

  // Pre-fill name from OAuth provider if available
  useEffect(() => {
    if (user?.displayName && !formData.name) {
      setFormData(prev => ({ ...prev, name: user.displayName || "" }))
    }
  }, [user, formData.name])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData((prev) => ({ ...prev, [id]: value }))
  }

  const handleProfilePictureSelect = (file: File) => {
    setProfilePicture(file)
  }

  const handleProfilePictureError = (error: string) => {
    toast({
      title: "Upload Error",
      description: error,
      variant: "destructive",
    })
  }

  const removeProfilePicture = () => {
    setProfilePicture(null)
  }

  const togglePreference = (
    preference: string,
    setter: React.Dispatch<React.SetStateAction<string[]>>,
    current: string[]
  ) => {
    if (current.includes(preference)) {
      setter(current.filter((p) => p !== preference))
    } else {
      setter([...current, preference])
    }
  }

  const nextStep = () => {
    if (step < totalSteps) {
      setStep(step + 1)
    }
  }

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  const handleCompleteProfile = async () => {
    if (!user) {
      toast({
        title: "Authentication Error",
        description: "Please sign in again to complete your profile",
        variant: "destructive",
      })
      router.push("/login")
      return
    }

    if (!formData.name) {
      toast({
        title: "Missing information",
        description: "Please provide your name",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    try {
      // Prepare profile picture form data if a file is selected
      let profilePictureFormData: FormData | undefined
      if (profilePicture) {
        profilePictureFormData = new FormData()
        profilePictureFormData.append("file", profilePicture)
      }

      // Complete user profile using server action
      const result = await completeUserProfileAction(
        {
          userId: user.uid,
          email: user.email || "",
          name: formData.name,
          bio: formData.bio,
          location: location || undefined,
          locationPlaceId: locationPlaceId || undefined,
          selectedTravelPreferences,
          budget,
          selectedAvailability,
          selectedMonths,
          selectedTravelGroups,
          referralCode: referralCode.trim() || undefined,
          // Include OAuth provider info
          photoURL: user.photoURL || undefined,
          authProvider: "google", // This could be dynamic based on how user signed in
        },
        profilePictureFormData
      )

      if (result.success) {
        toast({
          title: "Profile completed successfully!",
          description: "Welcome to Togeda.ai! Let's start planning your next adventure.",
        })

        // Redirect to callback URL or dashboard
        if (callbackUrl) {
          router.push(decodeURIComponent(callbackUrl))
        } else {
          router.push("/dashboard")
        }
      } else {
        toast({
          title: "Error completing profile",
          description: result.error || "Something went wrong. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error("Profile completion error:", error)
      toast({
        title: "Error completing profile",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  if (!user) {
    return null // This will be handled by the parent component
  }

  return (
    <div className="min-h-screen flex flex-col">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/">
            <Logo />
          </Link>
        </div>
      </header>

      <main className="flex-1 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Complete Your Profile</CardTitle>
            <CardDescription>
              Step {step} of {totalSteps}: {getStepDescription(step)}
              {invitedEmail && (
                <span className="block mt-2 text-sm text-primary">
                  Invited as: {invitedEmail}
                </span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {step === 1 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    placeholder="John Doe"
                    value={formData.name}
                    onChange={handleInputChange}
                  />
                  {user.displayName && (
                    <p className="text-xs text-muted-foreground">
                      Pre-filled from your {user.providerData?.[0]?.providerId === 'google.com' ? 'Google' : 'OAuth'} account
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="referralCode">Referral Code (Optional)</Label>
                  <Input
                    id="referralCode"
                    placeholder="Enter referral code"
                    value={referralCode}
                    onChange={(e) => setReferralCode(e.target.value.toUpperCase())}
                    maxLength={8}
                  />
                  {referralCode && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Using referral code: {referralCode} (Length: {referralCode.length})
                    </p>
                  )}
                </div>
              </div>
            )}

            {step === 2 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Travel Preferences</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {ALLOWED_TRAVEL_TYPES.map((pref) => (
                      <Button
                        key={pref}
                        variant={selectedTravelPreferences.includes(pref) ? "default" : "outline"}
                        className="justify-start"
                        onClick={() =>
                          togglePreference(
                            pref,
                            setSelectedTravelPreferences,
                            selectedTravelPreferences
                          )
                        }
                      >
                        {pref}
                      </Button>
                    ))}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="budget">Typical Budget</Label>
                  <select
                    id="budget"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={budget}
                    onChange={(e) => setBudget(e.target.value)}
                  >
                    <option value="budget-friendly">Budget-friendly</option>
                    <option value="mid-range">Mid-range</option>
                    <option value="luxury">Luxury</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label>Availability</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {ALLOWED_AVAILABILITY_PREFERENCES.map((avail) => (
                      <Button
                        key={avail}
                        variant={selectedAvailability.includes(avail) ? "default" : "outline"}
                        className="justify-start"
                        onClick={() =>
                          togglePreference(avail, setSelectedAvailability, selectedAvailability)
                        }
                      >
                        {avail}
                      </Button>
                    ))}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Preferred Travel Seasons</Label>
                  <MonthSelector
                    selectedMonths={selectedMonths}
                    onChange={setSelectedMonths}
                    onConfirm={() => {}}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Travel Group</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {ALLOWED_TRAVEL_GROUP_PREFERENCES.map((group) => (
                      <Button
                        key={group}
                        variant={selectedTravelGroups.includes(group) ? "default" : "outline"}
                        className="justify-start"
                        onClick={() =>
                          togglePreference(group, setSelectedTravelGroups, selectedTravelGroups)
                        }
                      >
                        {group}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {step === 3 && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Profile Picture (Optional)</Label>
                  {profilePicture ? (
                    <div className="flex items-center gap-4">
                      <div className="relative">
                        <img
                          src={URL.createObjectURL(profilePicture)}
                          alt="Profile preview"
                          className="w-20 h-20 rounded-full object-cover border-2 border-border"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                          onClick={removeProfilePicture}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{profilePicture?.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {profilePicture && (profilePicture.size / 1024).toFixed(1)} KB
                        </p>
                      </div>
                    </div>
                  ) : user.photoURL ? (
                    <div className="flex items-center gap-4">
                      <img
                        src={user.photoURL}
                        alt="Current profile"
                        className="w-20 h-20 rounded-full object-cover border-2 border-border"
                      />
                      <div className="flex-1">
                        <p className="text-sm font-medium">Using photo from your OAuth account</p>
                        <p className="text-xs text-muted-foreground">
                          You can upload a different photo if you prefer
                        </p>
                      </div>
                    </div>
                  ) : (
                    <ImageUploadWithCompression
                      preset="profilePicture"
                      onFileSelect={handleProfilePictureSelect}
                      onError={handleProfilePictureError}
                      showPreview={false}
                      showCompressionStats={false}
                      className="w-full"
                    />
                  )}
                </div>
                <LocationInputWithGeolocation
                  value={location}
                  onChange={(value, placeId) => {
                    setLocation(value)
                    setLocationPlaceId(placeId)
                  }}
                  placeholder="Where are you based?"
                  required={false}
                  allowUnauthenticated={true}
                  label="Location (Optional)"
                  showGeolocationButton={true}
                />
                <div className="space-y-2">
                  <Label htmlFor="bio">Bio (Optional)</Label>
                  <textarea
                    id="bio"
                    rows={3}
                    placeholder="Tell your friends a bit about yourself..."
                    className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={formData.bio}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={prevStep} disabled={step === 1}>
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </Button>
            {step < totalSteps ? (
              <Button onClick={nextStep}>
                Next <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            ) : (
              <Button onClick={handleCompleteProfile} disabled={loading}>
                {loading ? "Completing Profile..." : "Complete Setup"}
              </Button>
            )}
          </CardFooter>
        </Card>
      </main>
    </div>
  )
}

function getStepDescription(step: number) {
  switch (step) {
    case 1:
      return "Basic Information"
    case 2:
      return "Travel Preferences & Availability"
    case 3:
      return "Profile & Location"
    default:
      return ""
  }
}
