"use client"

import { Suspense } from "react"
import { ProfileCompletionForm } from "./components/profile-completion-form"
import { PageLoading } from "@/components/page-loading"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

function ProfileCompletionPageContent() {
  const user = useUser()
  const router = useRouter()

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!user) {
      router.push("/login")
    }
  }, [user, router])

  // Show loading while checking auth state
  if (!user) {
    return <PageLoading message="Loading..." />
  }

  return <ProfileCompletionForm />
}

export default function ProfileCompletionPage() {
  return (
    <Suspense fallback={<PageLoading message="Loading..." />}>
      <ProfileCompletionPageContent />
    </Suspense>
  )
}
