"use client"

import type React from "react"
import { useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Logo } from "@/components/ui/logo"
import { useAuthProviders, useProviderSignIn } from "@/lib/domains/auth/auth.hooks"
import { Loader2 } from "lucide-react"
import Link from "next/link"

/**
 * Provider icon component
 */
const ProviderIcon: React.FC<{ providerId: string; className?: string }> = ({ 
  providerId, 
  className = "h-5 w-5" 
}) => {
  switch (providerId) {
    case "google":
      return (
        <svg className={className} viewBox="0 0 24 24">
          <path
            fill="#4285f4"
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
          />
          <path
            fill="#34a853"
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
          />
          <path
            fill="#fbbc05"
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
          />
          <path
            fill="#ea4335"
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
          />
        </svg>
      )
    default:
      return <div className={`${className} bg-gray-300 rounded`} />
  }
}

/**
 * Modern provider-based signup form
 * This replaces the old email/password signup with OAuth-first approach
 */
export function ProviderSignupForm() {
  const searchParams = useSearchParams()
  const { providers, loading: providersLoading } = useAuthProviders()
  const { signInWithProvider, loading: signInLoading } = useProviderSignIn()

  // Get signup context from URL params
  const invitedEmail = searchParams.get("invited_email")
  const referralCode = searchParams.get("referral_code")
  const callbackUrl = searchParams.get("callback")

  const handleProviderSignUp = async (providerId: string) => {
    // Build redirect URL to profile completion if it's a new user
    let redirectUrl = "/profile-completion"
    
    // Add query parameters to profile completion
    const params = new URLSearchParams()
    if (invitedEmail) params.set("invited_email", invitedEmail)
    if (referralCode) params.set("referral_code", referralCode)
    if (callbackUrl) params.set("callback", callbackUrl)
    
    if (params.toString()) {
      redirectUrl += `?${params.toString()}`
    }

    await signInWithProvider({
      providerId,
      redirectUrl,
    })
  }

  if (providersLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col">
        <header className="p-4">
          <Logo />
        </header>
        <main className="flex-1 flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardContent className="flex items-center justify-center p-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </CardContent>
          </Card>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col">
      <header className="p-4">
        <Logo />
      </header>

      <main className="flex-1 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Join Togeda.ai</CardTitle>
            <CardDescription>
              {invitedEmail 
                ? `You've been invited to join Togeda.ai with ${invitedEmail}. Sign up to get started!`
                : "Create your account to start planning amazing trips with friends"
              }
              {referralCode && (
                <span className="block mt-2 text-sm font-medium text-primary">
                  Using referral code: {referralCode}
                </span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {providers.length > 0 ? (
              <>
                {providers.map((provider) => (
                  <Button
                    key={provider.id}
                    variant="outline"
                    className="w-full h-12 text-left justify-start gap-3"
                    onClick={() => handleProviderSignUp(provider.id)}
                    disabled={signInLoading || !provider.isConfigured}
                  >
                    {signInLoading ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : (
                      <ProviderIcon providerId={provider.id} />
                    )}
                    <span className="flex-1">
                      {signInLoading 
                        ? "Creating account..." 
                        : `Sign up with ${provider.metadata.displayName}`
                      }
                    </span>
                  </Button>
                ))}
                
                <div className="text-center text-sm text-muted-foreground mt-6">
                  <p>
                    By signing up, you agree to our{" "}
                    <a href="/terms" className="text-primary hover:underline">
                      Terms of Service
                    </a>{" "}
                    and{" "}
                    <a href="/privacy" className="text-primary hover:underline">
                      Privacy Policy
                    </a>
                  </p>
                </div>

                <div className="text-center text-sm mt-4">
                  Already have an account?{" "}
                  <Link 
                    href={`/login${callbackUrl ? `?callback=${encodeURIComponent(callbackUrl)}` : ''}`} 
                    className="text-primary hover:underline"
                  >
                    Sign In
                  </Link>
                </div>
              </>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  No authentication providers are currently available.
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  Please contact support if this issue persists.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
