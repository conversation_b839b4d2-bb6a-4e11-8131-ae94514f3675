import { auth } from "@/lib/firebase"
import { signOut } from "firebase/auth"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import { authProviderManager } from "./auth-provider-manager"
import type {
  SignInOptions,
  AuthProviderStatus,
  AccountLinkingStatus,
  SignInResult,
  LinkAccountResult,
} from "./auth.types"

/**
 * Modern Auth service with provider-agnostic architecture
 */
export class AuthService {
  /**
   * Get available authentication providers
   */
  static getAvailableProviders(): AuthProviderStatus[] {
    return authProviderManager.getAvailableProviders().map((metadata) => ({
      id: metadata.id,
      isConfigured: authProviderManager.isProviderReady(metadata.id),
      isEnabled: metadata.isEnabled,
      metadata,
    }))
  }

  /**
   * Get enabled authentication providers only
   */
  static getEnabledProviders(): AuthProviderStatus[] {
    return authProviderManager.getEnabledProviders().map((metadata) => ({
      id: metadata.id,
      isConfigured: authProviderManager.isProviderReady(metadata.id),
      isEnabled: metadata.isEnabled,
      metadata,
    }))
  }

  /**
   * Sign in with specified provider
   */
  static async signInWithProvider(options: SignInOptions): Promise<ServiceResponse<SignInResult>> {
    try {
      const provider = authProviderManager.getProvider(options.providerId)

      if (!provider) {
        return {
          success: false,
          error: new Error(`Provider '${options.providerId}' is not available`),
        }
      }

      if (!provider.isConfigured()) {
        return {
          success: false,
          error: new Error(`Provider '${options.providerId}' is not properly configured`),
        }
      }

      const result = await provider.signIn()

      if (result.success && result.data) {
        // Store the current provider for future reference
        localStorage.setItem("lastUsedAuthProvider", options.providerId)
      }

      return result
    } catch (error: any) {
      console.error(`Error signing in with ${options.providerId}:`, error)
      return { success: false, error }
    }
  }

  /**
   * Link account with specified provider
   */
  static async linkAccountWithProvider(
    providerId: string
  ): Promise<ServiceResponse<LinkAccountResult>> {
    try {
      const provider = authProviderManager.getProvider(providerId)

      if (!provider) {
        return {
          success: false,
          error: new Error(`Provider '${providerId}' is not available`),
        }
      }

      if (!provider.isConfigured()) {
        return {
          success: false,
          error: new Error(`Provider '${providerId}' is not properly configured`),
        }
      }

      return await provider.linkAccount()
    } catch (error: any) {
      console.error(`Error linking account with ${providerId}:`, error)
      return { success: false, error }
    }
  }

  /**
   * Get account linking status for current user
   */
  static async getAccountLinkingStatus(): Promise<ServiceResponse<AccountLinkingStatus>> {
    try {
      const currentUser = auth.currentUser
      if (!currentUser) {
        return {
          success: false,
          error: new Error("No user currently signed in"),
        }
      }

      const linkedProviders: string[] = []
      const availableForLinking: string[] = []

      // Check which providers are linked
      currentUser.providerData.forEach((profile) => {
        if (profile.providerId === "google.com") {
          linkedProviders.push("google")
        }
        // Add other provider checks as needed
      })

      // Get available providers that aren't linked yet
      const enabledProviders = this.getEnabledProviders()
      enabledProviders.forEach((provider) => {
        if (!linkedProviders.includes(provider.id)) {
          availableForLinking.push(provider.id)
        }
      })

      return {
        success: true,
        data: {
          isLinked: linkedProviders.length > 0,
          linkedProviders,
          availableForLinking,
        },
      }
    } catch (error: any) {
      console.error("Error getting account linking status:", error)
      return { success: false, error }
    }
  }

  /**
   * Log out the current user
   */
  static async logOut(): Promise<ServiceResponse> {
    try {
      // Sign out from all linked providers
      const enabledProviders = this.getEnabledProviders()
      await Promise.allSettled(
        enabledProviders.map(async (providerStatus) => {
          const provider = authProviderManager.getProvider(providerStatus.id)
          if (provider) {
            await provider.signOut()
          }
        })
      )

      // Sign out from Firebase
      await signOut(auth)

      // Clear stored provider info
      localStorage.removeItem("lastUsedAuthProvider")

      return { success: true }
    } catch (error) {
      console.error("Error logging out:", error)
      return { success: false, error }
    }
  }

  /**
   * Get the current authentication token
   */
  static async getAuthToken(): Promise<string | null> {
    try {
      const currentUser = auth.currentUser
      if (!currentUser) {
        return null
      }

      const token = await currentUser.getIdToken()
      return token
    } catch (error) {
      console.error("Error getting auth token:", error)
      return null
    }
  }

  /**
   * Get the last used authentication provider
   */
  static getLastUsedProvider(): string | null {
    if (typeof window !== "undefined") {
      return localStorage.getItem("lastUsedAuthProvider")
    }
    return null
  }

  /**
   * Check if a specific provider is available and configured
   */
  static isProviderAvailable(providerId: string): boolean {
    return authProviderManager.isProviderReady(providerId)
  }

  /**
   * Get provider metadata by ID
   */
  static getProviderMetadata(providerId: string) {
    const provider = authProviderManager.getProvider(providerId)
    return provider?.getMetadata() || null
  }
}
