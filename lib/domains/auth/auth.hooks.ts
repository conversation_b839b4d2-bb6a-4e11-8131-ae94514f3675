"use client"

import { useEffect, useState, useCallback } from "react"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import { auth } from "@/lib/firebase"
import { useAuthState } from "react-firebase-hooks/auth"
import { useAuthStore } from "./auth.store"
import { publicRoutes, type AuthProviderStatus, type SignInOptions } from "./auth.types"
import { UserService } from "../user/user.service"
import { AuthService } from "./auth.service"
import { toast } from "@/components/ui/use-toast"

// Export real-time hooks
export * from "./auth.realtime.hooks"

// Create selector hooks for specific parts of the auth state
export const useUser = () => useAuthStore((state) => state.user)
export const useUserData = () => useAuthStore((state) => state.userData)
export const useUserDataLoading = () => useAuthStore((state) => state.userDataLoading)
export const useUpdateUserData = () => useAuthStore((state) => state.updateUserData)
export const useAuthLoading = () => useAuthStore((state) => state.loading)
export const useAuthError = () => useAuthStore((state) => state.error)
export const useIsAdmin = () => useAuthStore((state) => state.isAdmin)

/**
 * Hook to sync Firebase auth state with Zustand store
 */
export function useAuthSync() {
  const [user, loading, error] = useAuthState(auth)
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // Get auth store actions
  const { setUser, setLoading, setError, isProtectedRoute, refreshAdminStatus } = useAuthStore()

  // Refresh admin status when user changes
  useEffect(() => {
    if (user) {
      refreshAdminStatus()
    }
  }, [user, refreshAdminStatus])

  // Update auth store with Firebase auth state
  useEffect(() => {
    setUser(user || null)
    setLoading(loading)
    setError(error || null)
  }, [user, loading, error, setUser, setLoading, setError])

  // Handle protected routes
  useEffect(() => {
    if (!loading && !user && isProtectedRoute(pathname)) {
      // Skip redirect for invitation pages - let them handle their own auth flow
      if (pathname?.startsWith("/invitation/")) {
        console.log(`Skipping auth redirect for invitation page: ${pathname}`)
        return
      }

      // Build the current URL with search params for callback
      const currentUrl = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : "")
      const callbackParam = encodeURIComponent(currentUrl)

      router.push(`/login?callback=${callbackParam}`)
    }
  }, [user, loading, pathname, searchParams, router, isProtectedRoute])

  return { user, loading, error }
}

/**
 * Hook to redirect authenticated users away from auth pages
 * Use this on login, signup, forgot-password, and landing pages
 */
export function useAuthRedirect() {
  const [user, loading] = useAuthState(auth)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Only redirect if user is authenticated and we're on an auth flow page
    if (!loading && user && publicRoutes.includes(pathname)) {
      // Check if user is new and should see welcome experience
      const checkAndRedirect = async () => {
        try {
          const isNewUser = await UserService.isNewUser(user.uid)
          if (isNewUser) {
            router.push("/welcome")
          } else {
            router.push("/dashboard")
          }
        } catch (error) {
          console.error("Error checking new user status:", error)
          // Fallback to dashboard on error
          router.push("/dashboard")
        }
      }

      checkAndRedirect()
    }
  }, [user, loading, pathname, router])

  return { user, loading, isRedirecting: !loading && !!user && publicRoutes.includes(pathname) }
}

/**
 * Hook for managing authentication providers
 */
export function useAuthProviders() {
  const [providers, setProviders] = useState<AuthProviderStatus[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadProviders = () => {
      try {
        const availableProviders = AuthService.getEnabledProviders()
        setProviders(availableProviders)
      } catch (error) {
        console.error("Error loading auth providers:", error)
      } finally {
        setLoading(false)
      }
    }

    loadProviders()
  }, [])

  return { providers, loading }
}

/**
 * Hook for signing in with providers
 */
export function useProviderSignIn() {
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()

  const signInWithProvider = useCallback(
    async (providerId: string | SignInOptions) => {
      setLoading(true)
      try {
        // Handle both string provider ID and options object for backward compatibility
        const options: SignInOptions = typeof providerId === "string" ? { providerId } : providerId

        const result = await AuthService.signInWithProvider(options)

        if (result.success && result.data) {
          // Return the result so the calling component can handle the redirect
          // This allows for better UX in the OAuth completion flow
          return result
        } else {
          const errorMessage =
            result.error instanceof Error
              ? result.error.message
              : "Failed to sign in. Please try again."

          return {
            success: false,
            error: errorMessage,
          }
        }
      } catch (error: any) {
        console.error("Sign in error:", error)
        const errorMessage = "An unexpected error occurred. Please try again."

        return {
          success: false,
          error: errorMessage,
        }
      } finally {
        setLoading(false)
      }
    },
    [router, searchParams]
  )

  return { signInWithProvider, loading }
}

/**
 * Hook for account linking
 */
export function useAccountLinking() {
  const [loading, setLoading] = useState(false)
  const [linkingStatus, setLinkingStatus] = useState<any>(null)

  const linkAccount = useCallback(async (providerId: string) => {
    setLoading(true)
    try {
      const result = await AuthService.linkAccountWithProvider(providerId)

      if (result.success) {
        toast({
          title: "Account linked",
          description: `Successfully linked your ${providerId} account.`,
        })

        // Refresh linking status
        const statusResult = await AuthService.getAccountLinkingStatus()
        if (statusResult.success) {
          setLinkingStatus(statusResult.data)
        }
      } else {
        const errorMessage =
          result.error instanceof Error
            ? result.error.message
            : "Failed to link account. Please try again."
        toast({
          title: "Linking failed",
          description: errorMessage,
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error("Account linking error:", error)
      toast({
        title: "Linking failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }, [])

  const refreshLinkingStatus = useCallback(async () => {
    try {
      const result = await AuthService.getAccountLinkingStatus()
      if (result.success) {
        setLinkingStatus(result.data)
      }
    } catch (error) {
      console.error("Error refreshing linking status:", error)
    }
  }, [])

  useEffect(() => {
    refreshLinkingStatus()
  }, [refreshLinkingStatus])

  return { linkAccount, linkingStatus, loading, refreshLinkingStatus }
}

/**
 * Convenience hook for getting user data from the auth store
 */
export function useUserWithData() {
  const user = useUser()
  const userData = useUserData()
  const loading = useUserDataLoading()
  // const fetchUserData = useAuthStore((state) => state.fetchUserData)

  // // Fetch user data when user changes or when the component mounts
  useEffect(() => {}, [user])

  return { user, userData, loading }
}

/**
 * Convenience hook for auth status (user and loading)
 */
export function useAuthStatus() {
  const user = useUser()
  const loading = useAuthLoading()
  return { user, loading }
}
