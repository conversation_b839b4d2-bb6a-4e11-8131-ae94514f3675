import type { User as FirebaseUser, UserCredential } from "firebase/auth"
import type { ServiceResponse } from "../../base/base.types"

/**
 * Provider configuration interface
 */
export interface ProviderConfig {
  clientId?: string
  clientSecret?: string
  scopes?: string[]
  customParameters?: Record<string, string>
  redirectUri?: string
}

/**
 * Provider metadata interface
 */
export interface ProviderMetadata {
  id: string
  name: string
  displayName: string
  iconName: string
  brandColor: string
  description: string
  isEnabled: boolean
}

/**
 * Sign-in result interface
 */
export interface SignInResult {
  user: FirebaseUser
  credential?: any
  isNewUser?: boolean
  additionalUserInfo?: any
}

/**
 * Account linking result interface
 */
export interface LinkAccountResult {
  success: boolean
  linked: boolean
  user?: FirebaseUser
  error?: Error
}

/**
 * Abstract base class for authentication providers
 * All OAuth providers must extend this class
 */
export abstract class BaseAuthProvider {
  protected config: ProviderConfig
  protected metadata: ProviderMetadata

  constructor(config: ProviderConfig, metadata: ProviderMetadata) {
    this.config = config
    this.metadata = metadata
  }

  /**
   * Get provider metadata
   */
  getMetadata(): ProviderMetadata {
    return this.metadata
  }

  /**
   * Get provider configuration
   */
  getConfig(): ProviderConfig {
    return this.config
  }

  /**
   * Update provider configuration
   */
  updateConfig(newConfig: Partial<ProviderConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * Check if provider is properly configured
   */
  abstract isConfigured(): boolean

  /**
   * Sign in with this provider
   */
  abstract signIn(): Promise<ServiceResponse<SignInResult>>

  /**
   * Link existing account with this provider
   */
  abstract linkAccount(): Promise<ServiceResponse<LinkAccountResult>>

  /**
   * Handle account linking during sign-in
   * This method checks if a user with the same email exists and links accounts
   */
  abstract handleAccountLinking(user: FirebaseUser): Promise<ServiceResponse<LinkAccountResult>>

  /**
   * Get access token for this provider (if supported)
   */
  abstract getAccessToken(): Promise<ServiceResponse<string>>

  /**
   * Refresh access token (if supported)
   */
  abstract refreshAccessToken(): Promise<ServiceResponse<string>>

  /**
   * Sign out from this provider (if needed)
   */
  abstract signOut(): Promise<ServiceResponse>

  /**
   * Validate provider-specific configuration
   */
  protected validateConfig(): void {
    if (!this.config.clientId && this.metadata.isEnabled) {
      throw new Error(`${this.metadata.displayName} provider requires clientId`)
    }
  }
}
