import {
  GoogleAuthProvider,
  signInWithPopup,
  linkWithCredential,
  type User as FirebaseUser,
} from "firebase/auth"
import { auth, db } from "@/lib/firebase"
import { doc, getDoc, updateDoc, serverTimestamp, query, collection, where, getDocs } from "firebase/firestore"
import { BaseAuthProvider, type ProviderConfig, type ProviderMetadata, type SignInResult, type LinkAccountResult } from "./base-provider"
import type { ServiceResponse } from "../../base/base.types"

/**
 * Google OAuth provider implementation
 */
export class GoogleProvider extends BaseAuthProvider {
  private provider: GoogleAuthProvider

  constructor(config: ProviderConfig) {
    const metadata: ProviderMetadata = {
      id: "google",
      name: "google",
      displayName: "Google",
      iconName: "google",
      brandColor: "#4285f4",
      description: "Sign in with your Google account",
      isEnabled: true,
    }

    super(config, metadata)
    this.provider = new GoogleAuthProvider()
    this.setupProvider()
  }

  /**
   * Setup Google provider with configuration
   */
  private setupProvider(): void {
    // Add default scopes
    this.provider.addScope("email")
    this.provider.addScope("profile")

    // Add additional scopes if configured
    if (this.config.scopes) {
      this.config.scopes.forEach(scope => {
        this.provider.addScope(scope)
      })
    }

    // Add custom parameters if configured
    if (this.config.customParameters) {
      this.provider.setCustomParameters(this.config.customParameters)
    }
  }

  /**
   * Check if provider is properly configured
   */
  isConfigured(): boolean {
    // Google provider works with Firebase default configuration
    // No additional client ID needed for basic functionality
    return true
  }

  /**
   * Sign in with Google
   */
  async signIn(): Promise<ServiceResponse<SignInResult>> {
    try {
      const result = await signInWithPopup(auth, this.provider)
      const credential = GoogleAuthProvider.credentialFromResult(result)
      
      // Handle account linking if user exists with same email
      const linkingResult = await this.handleAccountLinking(result.user)
      
      return {
        success: true,
        data: {
          user: result.user,
          credential,
          isNewUser: result.additionalUserInfo?.isNewUser,
          additionalUserInfo: result.additionalUserInfo,
        },
      }
    } catch (error: any) {
      console.error("Google sign-in error:", error)
      
      let errorMessage = "Failed to sign in with Google"
      
      switch (error.code) {
        case "auth/popup-closed-by-user":
          errorMessage = "Sign-in was cancelled"
          break
        case "auth/popup-blocked":
          errorMessage = "Pop-up was blocked by your browser"
          break
        case "auth/account-exists-with-different-credential":
          errorMessage = "An account already exists with this email using a different sign-in method"
          break
        default:
          errorMessage = error.message || "Failed to sign in with Google"
      }
      
      return { success: false, error: new Error(errorMessage) }
    }
  }

  /**
   * Link existing account with Google
   */
  async linkAccount(): Promise<ServiceResponse<LinkAccountResult>> {
    try {
      const currentUser = auth.currentUser
      if (!currentUser) {
        return { success: false, error: new Error("No user currently signed in") }
      }

      const result = await signInWithPopup(auth, this.provider)
      const credential = GoogleAuthProvider.credentialFromResult(result)
      
      if (credential) {
        await linkWithCredential(currentUser, credential)
        
        // Update user document with Google linking info
        await this.updateUserDocumentWithGoogleInfo(currentUser, result.user)
        
        return {
          success: true,
          data: {
            success: true,
            linked: true,
            user: currentUser,
          },
        }
      }
      
      return { success: false, error: new Error("Failed to get Google credential") }
    } catch (error: any) {
      console.error("Google account linking error:", error)
      return { success: false, error }
    }
  }

  /**
   * Handle account linking during sign-in
   */
  async handleAccountLinking(user: FirebaseUser): Promise<ServiceResponse<LinkAccountResult>> {
    try {
      if (!user.email) {
        return {
          success: true,
          data: { success: true, linked: false, user },
        }
      }

      // Check if user document exists with this email
      const existingUserQuery = await getDocs(
        query(collection(db, "users"), where("email", "==", user.email))
      )

      if (!existingUserQuery.empty) {
        // User exists - this is an account linking scenario
        const existingUserDoc = existingUserQuery.docs[0]
        const existingUserData = existingUserDoc.data()

        // Update existing user document with Google provider info
        await updateDoc(doc(db, "users", user.uid), {
          ...existingUserData,
          photoURL: user.photoURL || existingUserData.photoURL,
          displayName: user.displayName || existingUserData.displayName,
          googleLinked: true,
          googleLinkedAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })

        return {
          success: true,
          data: { success: true, linked: true, user },
        }
      }

      return {
        success: true,
        data: { success: true, linked: false, user },
      }
    } catch (error: any) {
      console.error("Error in Google account linking:", error)
      return { success: false, error }
    }
  }

  /**
   * Update user document with Google information
   */
  private async updateUserDocumentWithGoogleInfo(currentUser: FirebaseUser, googleUser: FirebaseUser): Promise<void> {
    try {
      const userRef = doc(db, "users", currentUser.uid)
      const userDoc = await getDoc(userRef)
      
      if (userDoc.exists()) {
        await updateDoc(userRef, {
          photoURL: googleUser.photoURL || currentUser.photoURL,
          displayName: googleUser.displayName || currentUser.displayName,
          googleLinked: true,
          googleLinkedAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })
      }
    } catch (error) {
      console.error("Error updating user document with Google info:", error)
      // Don't throw - this is not critical for the linking process
    }
  }

  /**
   * Get access token for Google (not implemented - would require additional setup)
   */
  async getAccessToken(): Promise<ServiceResponse<string>> {
    return { success: false, error: new Error("Access token retrieval not implemented for Google provider") }
  }

  /**
   * Refresh access token (not implemented)
   */
  async refreshAccessToken(): Promise<ServiceResponse<string>> {
    return { success: false, error: new Error("Token refresh not implemented for Google provider") }
  }

  /**
   * Sign out from Google (Firebase handles this automatically)
   */
  async signOut(): Promise<ServiceResponse> {
    // Firebase Auth handles Google sign-out automatically
    return { success: true }
  }
}
