import { BaseAuthProvider, type ProviderConfig, type ProviderMetadata } from "./providers/base-provider"
import { GoogleProvider } from "./providers/google-provider"

/**
 * Provider registry type
 */
type ProviderRegistry = {
  [key: string]: new (config: ProviderConfig) => BaseAuthProvider
}

/**
 * Provider configuration map
 */
interface ProviderConfigMap {
  [providerId: string]: ProviderConfig
}

/**
 * Authentication Provider Manager
 * Handles registration, configuration, and instantiation of auth providers
 */
export class AuthProviderManager {
  private static instance: AuthProviderManager
  private providers: Map<string, BaseAuthProvider> = new Map()
  private registry: ProviderRegistry = {}
  private configs: ProviderConfigMap = {}

  private constructor() {
    this.registerDefaultProviders()
  }

  /**
   * Get singleton instance
   */
  static getInstance(): AuthProviderManager {
    if (!AuthProviderManager.instance) {
      AuthProviderManager.instance = new AuthProviderManager()
    }
    return AuthProviderManager.instance
  }

  /**
   * Register default providers
   */
  private registerDefaultProviders(): void {
    this.registry.google = GoogleProvider
    
    // Future providers can be registered here:
    // this.registry.github = GitHubProvider
    // this.registry.apple = AppleProvider
    // this.registry.facebook = FacebookProvider
  }

  /**
   * Configure providers with their settings
   */
  configureProviders(configs: ProviderConfigMap): void {
    this.configs = { ...this.configs, ...configs }
    
    // Re-initialize configured providers
    Object.keys(configs).forEach(providerId => {
      if (this.registry[providerId]) {
        this.initializeProvider(providerId)
      }
    })
  }

  /**
   * Initialize a specific provider
   */
  private initializeProvider(providerId: string): void {
    const ProviderClass = this.registry[providerId]
    const config = this.configs[providerId] || {}
    
    if (ProviderClass) {
      const provider = new ProviderClass(config)
      this.providers.set(providerId, provider)
    }
  }

  /**
   * Get a provider instance by ID
   */
  getProvider(providerId: string): BaseAuthProvider | null {
    // Initialize provider if not already done
    if (!this.providers.has(providerId) && this.registry[providerId]) {
      this.initializeProvider(providerId)
    }
    
    return this.providers.get(providerId) || null
  }

  /**
   * Get all available provider metadata
   */
  getAvailableProviders(): ProviderMetadata[] {
    return Object.keys(this.registry).map(providerId => {
      const provider = this.getProvider(providerId)
      return provider?.getMetadata() || {
        id: providerId,
        name: providerId,
        displayName: providerId,
        iconName: providerId,
        brandColor: "#000000",
        description: `Sign in with ${providerId}`,
        isEnabled: false,
      }
    })
  }

  /**
   * Get enabled providers only
   */
  getEnabledProviders(): ProviderMetadata[] {
    return this.getAvailableProviders().filter(provider => {
      const instance = this.getProvider(provider.id)
      return instance?.isConfigured() && provider.isEnabled
    })
  }

  /**
   * Register a new provider class
   */
  registerProvider(providerId: string, ProviderClass: new (config: ProviderConfig) => BaseAuthProvider): void {
    this.registry[providerId] = ProviderClass
  }

  /**
   * Check if a provider is registered
   */
  isProviderRegistered(providerId: string): boolean {
    return providerId in this.registry
  }

  /**
   * Check if a provider is configured and ready to use
   */
  isProviderReady(providerId: string): boolean {
    const provider = this.getProvider(providerId)
    return provider?.isConfigured() ?? false
  }

  /**
   * Get provider configuration
   */
  getProviderConfig(providerId: string): ProviderConfig | null {
    return this.configs[providerId] || null
  }

  /**
   * Update provider configuration
   */
  updateProviderConfig(providerId: string, config: Partial<ProviderConfig>): void {
    this.configs[providerId] = { ...this.configs[providerId], ...config }
    
    // Re-initialize the provider with new config
    if (this.providers.has(providerId)) {
      this.initializeProvider(providerId)
    }
  }

  /**
   * Remove a provider
   */
  removeProvider(providerId: string): void {
    this.providers.delete(providerId)
    delete this.configs[providerId]
  }

  /**
   * Clear all providers (useful for testing)
   */
  clearProviders(): void {
    this.providers.clear()
    this.configs = {}
  }
}

/**
 * Default provider manager instance
 */
export const authProviderManager = AuthProviderManager.getInstance()

/**
 * Initialize providers with default configuration
 */
authProviderManager.configureProviders({
  google: {
    scopes: ["email", "profile"],
    customParameters: {
      prompt: "select_account",
    },
  },
  // Future provider configurations:
  // github: {
  //   scopes: ["user:email"],
  // },
  // apple: {
  //   scopes: ["name", "email"],
  // },
})
